import { redis } from "../config/redis.js";
import { pubsub } from "../config/redis.js";
import { query } from "../config/database.js";
import { KolFeed } from "../workers/kolFeed.js";
import { KafkaStreams } from "../workers/kafkaStream.js";
import { SolanaTracker } from "../workers/solanaTracker.js";

export class StreamManager {
  constructor() {
    this.streams = new Map();
    this.intervals = new Map();
    this.isRunning = false;
    this.kolFeed = null;
    this.kafkaStreams = null;
    this.solanaTracker = null;

    this.initializeStreams();
  }

  // Initialize streams
  initializeStreams() {
    (async () => {
      // reset all websocket sessions
      await query(
        "UPDATE websocket_sessions SET disconnected_at = NOW() WHERE disconnected_at IS NULL"
      );
      // clear all ws_connections:* keys from Redis
      const keys = await redis.keys('ws_connections:*');
      if (keys.length > 0) {
        await redis.del(...keys);
        console.log(`✅ Cleared ${keys.length} ws_connections cache keys`);
      } else {
        console.log('ℹ️ No ws_connections cache keys found');
      }
    })();

    Promise.all([
      // KOL Feed stream - real-time KOL trading activity
      this.registerStream("kol-feed", {
        isEventDriven: true,
      }),
      // Jupiter AMM swaps stream - real-time Jupiter AMM swaps
      this.registerStream("jupiter-amm-swaps", {
        isEventDriven: true,
      }),
      // Pumpfun AMM swaps stream - real-time Pumpfun AMM swaps
      this.registerStream("pumpfun-amm-swaps", {
        isEventDriven: true,
      }),
      // Jupiter DCA orders stream - real-time Jupiter DCA orders
      this.registerStream("jupiter-dca-orders", {
        isEventDriven: true,
      }),
      // SolanaTracker streams - real-time Solana data from SolanaTracker API
      this.registerStream("tokens-launched", {
        isEventDriven: true,
      }),
      this.registerStream("tokens-graduating", {
        isEventDriven: true,
      }),
      this.registerStream("tokens-graduated", {
        isEventDriven: true,
      }),
      this.registerStream("pool-changes", {
        isEventDriven: true,
      }),
      this.registerStream("token-transactions", {
        isEventDriven: true,
      }),
      this.registerStream("price-updates", {
        isEventDriven: true,
      }),
      this.registerStream("wallet-transactions", {
        isEventDriven: true,
      }),
      this.registerStream("token-metadata", {
        isEventDriven: true,
      }),
      this.registerStream("token-holders", {
        isEventDriven: true,
      }),
      this.registerStream("token-changes", {
        isEventDriven: true,
      }),
    ])


    console.log("✅ Stream Manager initialized with all streams");
  }

  // Register a new stream
  registerStream(streamName, config) {
    this.streams.set(streamName, {
      name: streamName,
      interval: config.interval || 0,
      generator: config.generator || null,
      isEventDriven: config.isEventDriven || false,
      isActive: false,
      lastUpdate: null,
      messageCount: 0,
    });
  }

  // Start all streams
  async start() {
    if (this.isRunning) {
      console.log("Stream Manager is already running");
      return;
    }

    this.isRunning = true;

    const [kolFeed, kafkaStreams, solanaTracker] = await Promise.all([
      new KolFeed(),
      new KafkaStreams(),
      new SolanaTracker(),
    ]);

    // Initialize workers
    this.kolFeed = kolFeed;
    this.kafkaStreams = kafkaStreams;
    this.solanaTracker = solanaTracker;

    await Promise.all([
      this.kolFeed.init(),
      this.kafkaStreams.init(),
      this.solanaTracker.init(),
    ])

    // Set up Redis subscriptions for internal messages
    this.setupKolFeedSubscription();
    this.setupKafkaStreamsSubscription();
    this.setupSolanaTrackerSubscription();

    console.log("✅ All workers initialized and connected");

    // Start all streams
    for (const [streamName, stream] of this.streams) {
      if (stream.isEventDriven) {
        // Mark event-driven streams as active
        stream.isActive = true;
        console.log(`✅ Event-driven stream started: ${streamName}`);
      } else if (stream.generator) {
        // Start interval-based streams
        this.startIntervalStream(streamName);
      } else {
        console.warn(
          `⚠️ Stream ${streamName} has no generator and is not event-driven`
        );
      }
    }

    console.log("✅ All streams started");
  }

  // Stop all streams
  async stop() {
    if (!this.isRunning) {
      console.log("Stream Manager is not running");
      return;
    }

    this.isRunning = false;

    // Stop all workers
    if (this.kolFeed) {
      this.kolFeed.stop();
      this.kolFeed = null;
    }

    if (this.kafkaStreams) {
      this.kafkaStreams.stop();
      this.kafkaStreams = null;
    }

    if (this.solanaTracker) {
      this.solanaTracker.stop();
      this.solanaTracker = null;
    }

    // Stop all interval-based streams
    for (const [streamName, stream] of this.streams) {
      if (!stream.isActive) {
        console.log(`Stream ${streamName} is not active`);
        continue;
      }

      this.stopIntervalStream(streamName);
    }

    console.log("🛑 All streams stopped");
  }

  // Start an interval-based stream
  startIntervalStream(streamName) {
    const stream = this.streams.get(streamName);
    if (!stream) {
      console.error(`Stream not found: ${streamName}`);
      return;
    }

    if (stream.isActive) {
      console.log(`Stream ${streamName} is already active`);
      return;
    }

    if (stream.isEventDriven) {
      console.log(
        `Cannot start event-driven stream ${streamName} with interval`
      );
      return;
    }

    const interval = setInterval(async () => {
      try {
        const data = await stream.generator();
        await this.publishStreamData(streamName, data);

        stream.lastUpdate = Date.now();
        stream.messageCount++;
      } catch (error) {
        console.error(`Error generating data for stream ${streamName}:`, error);
      }
    }, stream.interval);

    this.intervals.set(streamName, interval);
    stream.isActive = true;

    console.log(
      `✅ Interval stream started: ${streamName} (interval: ${stream.interval}ms)`
    );
  }

  // Stop an interval-based stream
  stopIntervalStream(streamName) {
    const stream = this.streams.get(streamName);
    if (!stream) {
      console.error(`Stream not found: ${streamName}`);
      return;
    }

    if (!stream.isActive) {
      console.log(`Stream ${streamName} is not active`);
      return;
    }

    const interval = this.intervals.get(streamName);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(streamName);
    }

    stream.isActive = false;
    console.log(`🛑 Interval stream stopped: ${streamName}`);
  }

  // Set up Redis subscription for KOL feed internal messages
  setupKolFeedSubscription() {
    // Subscribe to the internal KOL feed channel
    pubsub.subscribe("kol_feed_internal", (data) => {
      try {
        // console.log("📨 Received KOL feed internal message");

        // Handle the KOL feed data through the event-driven system
        this.handleEventDrivenData("kol-feed", data);
      } catch (error) {
        console.error("❌ Error handling KOL feed internal message:", error);
      }
    });

    console.log("✅ KOL Feed Redis subscription setup");
  }

  setupKafkaStreamsSubscription() {
    // Subscribe to the internal KOL feed channel
    pubsub.subscribe("kafka_streams_internal", (data) => {
      try {
        // console.log("📨 Received Kafka Streams internal message");

        // Handle the KOL feed data through the event-driven system
        this.handleEventDrivenData(data.stream, data.data);
      } catch (error) {
        console.error("❌ Error handling Kafka Streams internal message:", error);
      }
    });

    console.log("✅ Kafka Streams Redis subscription setup");
  }

  setupSolanaTrackerSubscription() {
    // Subscribe to the internal SolanaTracker channel
    pubsub.subscribe("solana_tracker_internal", (data) => {
      try {
        // console.log("📨 Received SolanaTracker internal message");

        // Map room types to stream names
        const roomToStreamMap = {
          'latest': 'tokens-launched',
          'graduating': 'tokens-graduating',
          'graduated': 'tokens-graduated',
          'pool': 'pool-changes',
          'transaction': 'token-transactions',
          'price': 'price-updates',
          'wallet': 'wallet-transactions',
          'metadata': 'token-metadata',
          'holders': 'token-holders',
          'token': 'token-changes'
        };

        const roomType = data.room.split(':')[0];
        const streamName = roomToStreamMap[roomType];

        if (streamName) {
          // Handle the SolanaTracker data through the event-driven system
          this.handleEventDrivenData(streamName, data.data);
        } else {
          console.warn(`⚠️ [StreamManager] Unknown SolanaTracker room type: ${roomType}`);
        }
      } catch (error) {
        console.error("❌ Error handling SolanaTracker internal message:", error);
      }
    });

    console.log("✅ SolanaTracker Redis subscription setup");
  }

  // Handle event-driven data
  async handleEventDrivenData(streamName, data) {
    const stream = this.streams.get(streamName);
    if (!stream || !stream.isActive) {
      return;
    }

    try {
      // Publish the data directly without additional wrapping
      await pubsub.publish("stream_data", {
        stream: streamName,
        data: data,
        timestamp: Date.now(),
      });
      stream.lastUpdate = Date.now();
      stream.messageCount++;
    } catch (error) {
      console.error(
        `Error handling event-driven data for stream ${streamName}:`,
        error
      );
    }
  }

  // Publish data to a stream via Redis
  async publishStreamData(streamName, data) {
    try {
      await pubsub.publish("stream_data", {
        stream: streamName,
        data,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error(`Error publishing to stream ${streamName}:`, error);
    }
  }



  // Get stream statistics
  getStreamStats() {
    const stats = {};

    for (const [streamName, stream] of this.streams) {
      stats[streamName] = {
        name: streamName,
        isActive: stream.isActive,
        interval: stream.interval,
        lastUpdate: stream.lastUpdate,
        messageCount: stream.messageCount,
        uptime:
          stream.isActive && stream.lastUpdate
            ? Date.now() - stream.lastUpdate
            : 0,
      };
    }

    return stats;
  }

  // Get list of available streams
  getAvailableStreams() {
    return Array.from(this.streams.keys());
  }

  // Check if stream exists
  streamExists(streamName) {
    return this.streams.has(streamName);
  }

  // Subscribe to SolanaTracker room (for dynamic subscriptions)
  async subscribeSolanaTrackerRoom(room, subscriberId) {
    if (!this.solanaTracker) {
      throw new Error("SolanaTracker worker is not initialized");
    }

    return await this.solanaTracker.subscribe(room, subscriberId);
  }

  // Unsubscribe from SolanaTracker room
  async unsubscribeSolanaTrackerRoom(room, subscriberId) {
    if (!this.solanaTracker) {
      throw new Error("SolanaTracker worker is not initialized");
    }

    return await this.solanaTracker.unsubscribe(room, subscriberId);
  }

  // Get SolanaTracker statistics
  getSolanaTrackerStats() {
    if (!this.solanaTracker) {
      return { error: "SolanaTracker worker is not initialized" };
    }

    return this.solanaTracker.getStats();
  }

  // Get available SolanaTracker room types
  getAvailableSolanaTrackerRooms() {
    if (!this.solanaTracker) {
      return [];
    }

    return this.solanaTracker.getAvailableRoomTypes();
  }


}
