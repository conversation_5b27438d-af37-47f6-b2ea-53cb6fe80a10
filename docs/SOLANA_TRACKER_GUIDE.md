# SolanaTracker Worker Guide

## Overview

The SolanaTracker worker (`src/workers/solanaTracker.js`) provides an efficient proxy for the SolanaTracker WebSocket API with advanced connection pooling capabilities. This worker enables real-time streaming of Solana blockchain data including token launches, price updates, transactions, and more.

## Key Features

### 🔄 Connection Pooling
- **Shared Connections**: Multiple users can subscribe to the same SolanaTracker endpoint without creating duplicate connections
- **Automatic Management**: Connections are created only when needed and closed when no subscribers remain
- **Resource Efficient**: Minimizes WebSocket connections to SolanaTracker API

### 🔧 Modular Architecture
- **Discrete Transformation Functions**: Each data type has its own transformation function for easy maintenance
- **Abstracted Data Sources**: Implementation details are hidden, making future refactoring simple
- **Event-Driven Integration**: Seamlessly integrates with existing StreamManager system

### 🛡️ Robust Error Handling
- **Exponential Backoff**: Intelligent reconnection strategy with jitter
- **Deduplication**: Prevents duplicate transaction processing
- **Graceful Degradation**: Handles connection failures without affecting other streams

## Architecture

### Data Flow

```
SolanaTracker WebSocket API → SolanaTracker Worker → Redis Pub/Sub → Stream Manager → WebSocket Clients
```

### Components

1. **SolanaTracker Worker** (`src/workers/solanaTracker.js`)
   - Manages WebSocket connections to SolanaTracker API
   - Implements connection pooling and subscriber management
   - Transforms raw data to standardized API format
   - Publishes to internal Redis channel

2. **Connection Pool Manager**
   - Tracks active connections by room/endpoint
   - Manages subscriber lists for each room
   - Handles connection lifecycle and cleanup

3. **Stream Manager Integration**
   - Subscribes to internal Redis channel
   - Broadcasts to WebSocket clients
   - Handles credit deduction per message (0 credits for Basic+ tiers)

## Configuration

### Environment Variables

```bash
# Required for SolanaTracker
SOLANA_TRACKER_WSS_URL=wss://datastream.solanatracker.io
SOLANA_TRACKER_WSS_KEY=your-api-key-here

# Optional Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
```

### Supported Room Types

| Room Type | Description | Requires Parameters | Example | Stream Name |
|-----------|-------------|-------------------|---------|-------------|
| `latest` | Latest tokens/pools | No | `latest` | `tokens-launched` |
| `graduating` | Graduating tokens | No | `graduating` or `graduating:sol:175` | `tokens-graduating` |
| `graduated` | Graduated tokens | No | `graduated` | `tokens-graduated` |
| `pool` | Pool changes | Yes | `pool:poolId` | `pool-changes` |
| `transaction` | Token transactions | Yes | `transaction:tokenAddress` | `token-transactions` |
| `price` | Price updates | Yes | `price:poolId`, `price-by-token:tokenId`, `price:tokenId` | `price-updates` |
| `wallet` | Wallet transactions | Yes | `wallet:walletAddress` | `wallet-transactions` |
| `metadata` | Token metadata (BETA) | Yes | `metadata:tokenAddress` | `token-metadata` |
| `holders` | Token holders (BETA) | Yes | `holders:tokenAddress` | `token-holders` |
| `token` | Token changes | Yes | `token:tokenAddress` | `token-changes` |

### Detailed Room Descriptions

#### Latest Tokens/Pools (`latest` → `tokens-launched`)
- **Description**: Receive updates about the latest tokens and pools launched on Solana
- **Parameters**: None required
- **Usage**: `latest`
- **Data**: Complete token information, pool details, risk analysis, price events

#### Graduating Tokens (`graduating` → `tokens-graduating`)
- **Description**: Receive updates on tokens that are close to completing their bonding curve on Pump.fun/Moonshot
- **Parameters**: Optional market cap threshold
- **Usage**: `graduating` or `graduating:sol:175` (for specific SOL threshold)
- **Data**: Token details, bonding curve progress, graduation status

#### Graduated Tokens (`graduated` → `tokens-graduated`)
- **Description**: Receive updates on tokens that have recently completed their bonding curve
- **Parameters**: None required
- **Usage**: `graduated`
- **Data**: Newly graduated token information, final pool details

#### Pool Changes (`pool:poolId` → `pool-changes`)
- **Description**: Receive real-time updates about changes in a specific pool
- **Parameters**: Pool ID required
- **Usage**: `pool:poolIdHere`
- **Data**: Liquidity changes, price updates, volume changes

#### Token Transactions (`transaction:tokenAddress` → `token-transactions`)
- **Description**: Receive updates on the latest transactions for a specific token
- **Parameters**: Token address required
- **Usage**: `transaction:tokenAddressHere`
- **Data**: Transaction details, amounts, wallet addresses, timestamps

#### Price Updates (`price:*` → `price-updates`)
- **Description**: Receive price updates for specific pools or tokens
- **Parameters**: Pool ID or Token ID required
- **Usage**:
  - `price:poolId` - Uses specific pool for price updates
  - `price-by-token:tokenId` - Uses pool with highest liquidity
  - `price:tokenId` - Uses all available pools
- **Data**: Current prices, price changes, volume data

#### Wallet Transactions (`wallet:walletAddress` → `wallet-transactions`)
- **Description**: Receive updates about transactions for a specific wallet
- **Parameters**: Wallet address required
- **Usage**: `wallet:walletAddressHere`
- **Data**: All transactions involving the specified wallet

#### Token Metadata (`metadata:tokenAddress` → `token-metadata`) **[BETA]**
- **Description**: Retrieve complete token metadata for a given address
- **Parameters**: Token address required
- **Usage**: `metadata:tokenAddressHere`
- **Data**: Complete metadata including IPFS data, attributes, properties
- **Note**: Useful when initial response lacks full details due to pending IPFS propagation

#### Token Holders (`holders:tokenAddress` → `token-holders`) **[BETA]**
- **Description**: Receive updates on holder count changes for a token
- **Parameters**: Token address required
- **Usage**: `holders:tokenAddressHere`
- **Data**: Holder count changes, distribution updates
- **Note**: Must have been requested by `/tokens/:token/holders` endpoint first

#### Token Changes (`token:tokenAddress` → `token-changes`)
- **Description**: Receive all updates from any pool for a specific token
- **Parameters**: Token address required
- **Usage**: `token:tokenAddressHere`
- **Data**: Comprehensive updates including price, volume, liquidity, transactions

## Usage Examples

### Basic Subscription

```javascript
// Subscribe to latest tokens (basic stream)
const result = await streamManager.subscribeSolanaTrackerRoom('latest', 'user123');
console.log(`Subscribed to latest tokens. Total subscribers: ${result.subscriberCount}`);

// Subscribe to specific token transactions (parameterized stream)
await streamManager.subscribeSolanaTrackerRoom(
  'transaction:So11111111111111111111111111111111111111112',
  'user123'
);

// Subscribe to pool changes (parameterized stream)
await streamManager.subscribeSolanaTrackerRoom(
  'pool:poolIdHere',
  'user123'
);

// Subscribe to wallet transactions (parameterized stream)
await streamManager.subscribeSolanaTrackerRoom(
  'wallet:walletAddressHere',
  'user123'
);

// Subscribe to price updates with different formats
await streamManager.subscribeSolanaTrackerRoom('price:poolId', 'user123');
await streamManager.subscribeSolanaTrackerRoom('price-by-token:tokenId', 'user123');
await streamManager.subscribeSolanaTrackerRoom('price:tokenId', 'user123');

// Subscribe to graduating tokens with market cap threshold
await streamManager.subscribeSolanaTrackerRoom('graduating:sol:175', 'user123');
```

### Unsubscription

```javascript
// Unsubscribe from room
const result = await streamManager.unsubscribeSolanaTrackerRoom('latest', 'user123');
console.log(`Remaining subscribers: ${result.subscriberCount}`);
```

### Getting Statistics

```javascript
// Get worker statistics
const stats = streamManager.getSolanaTrackerStats();
console.log('Active connections:', stats.activeConnections);
console.log('Total subscribers:', stats.totalSubscribers);
console.log('Room details:', stats.rooms);
```

## Data Transformation

### Latest Tokens Example

**Input (SolanaTracker):**
```json
{
  "token": {
    "name": "Example Token",
    "symbol": "EXAMPLE",
    "mint": "TokenMintAddress...",
    "decimals": 6
  },
  "pools": [...],
  "events": {...},
  "risk": {...}
}
```

**Output (Standardized):**
```json
{
  "type": "latest",
  "timestamp": 1703123456789,
  "token": {
    "name": "Example Token",
    "symbol": "EXAMPLE",
    "mint": "TokenMintAddress...",
    "decimals": 6
  },
  "pools": [...],
  "events": {...},
  "risk": {...}
}
```

### Transaction Data Example

**Input (SolanaTracker):**
```json
{
  "tx": "TransactionSignature...",
  "tokenAddress": "TokenAddress...",
  "type": "buy",
  "amount": 1000000,
  "price": 0.001
}
```

**Output (Standardized):**
```json
{
  "type": "transaction",
  "timestamp": 1703123456789,
  "tx": "TransactionSignature...",
  "tokenAddress": "TokenAddress...",
  "transactionType": "buy",
  "amount": 1000000,
  "price": 0.001
}
```

## Integration with StreamManager

The SolanaTracker worker integrates seamlessly with the existing StreamManager:

1. **Initialization**: Worker is created and initialized alongside KolFeed and KafkaStreams
2. **Event-Driven Streams**: Registered as event-driven streams in StreamManager
3. **Redis Pub/Sub**: Uses internal Redis channel for communication
4. **Credit System**: Premium+ tier feature with 0 credit cost per message

## API Endpoints

### WebSocket Streams

The following streams are automatically available through the WebSocket API:

**Basic Streams (No Parameters Required):**
- `tokens-launched` - Latest tokens and pools (Basic+ tier, 0 credits)
- `tokens-graduating` - Tokens approaching graduation (Basic+ tier, 0 credits)
- `tokens-graduated` - Recently graduated tokens (Basic+ tier, 0 credits)

**Parameterized Streams (Require Specific Parameters):**
- `pool-changes` - Real-time updates for specific pools (Basic+ tier, 0 credits)
- `token-transactions` - Transaction updates for specific tokens (Basic+ tier, 0 credits)
- `price-updates` - Price updates for specific pools/tokens (Basic+ tier, 0 credits)
- `wallet-transactions` - Transaction updates for specific wallets (Basic+ tier, 0 credits)
- `token-metadata` - Token metadata updates (BETA, Basic+ tier, 0 credits)
- `token-holders` - Token holder count changes (BETA, Basic+ tier, 0 credits)
- `token-changes` - All updates for specific tokens (Basic+ tier, 0 credits)

**Note**: Historical data endpoints are not available for SolanaTracker streams. These streams provide real-time data only.

## Monitoring and Maintenance

### Health Checks

```javascript
// Check if worker is running
const stats = streamManager.getSolanaTrackerStats();
console.log('Worker status:', stats.isRunning);

// Check connection health
for (const [room, details] of Object.entries(stats.rooms)) {
  if (!details.isConnected) {
    console.warn(`Connection issue in room: ${room}`);
  }
}
```

### Maintenance Tasks

The worker automatically performs maintenance:

- **Transaction Deduplication**: Cleans up old transaction IDs
- **Connection Cleanup**: Removes unused connections
- **Reconnection Management**: Handles failed connections with exponential backoff

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check `SOLANA_TRACKER_WSS_URL` and `SOLANA_TRACKER_WSS_KEY`
   - Verify SolanaTracker API subscription status
   - Check network connectivity

2. **No Data Received**
   - Verify room name format (e.g., `latest`, `transaction:tokenAddress`)
   - Check SolanaTracker API rate limits
   - Review worker logs for errors

3. **High Memory Usage**
   - Monitor transaction deduplication set size
   - Check for connection leaks in pool
   - Review subscriber cleanup

### Debug Logging

Enable debug logging in development:

```bash
NODE_ENV=development
```

This will show detailed logs for:
- Connection establishment
- Message processing
- Subscriber management
- Reconnection attempts

## Performance Considerations

### Connection Pooling Benefits

- **Reduced API Calls**: Shared connections minimize SolanaTracker API usage
- **Lower Latency**: Existing connections provide faster data delivery
- **Resource Efficiency**: Fewer WebSocket connections reduce memory usage

### Scalability

- **Horizontal Scaling**: Multiple instances can run with Redis coordination
- **Load Distribution**: Connection pooling distributes load efficiently
- **Memory Management**: Automatic cleanup prevents memory leaks

## Security

### API Key Management

- Store `SOLANA_TRACKER_WSS_KEY` securely in environment variables
- Rotate API keys regularly
- Monitor API usage for anomalies

### Data Validation

- All incoming data is validated and transformed
- Error handling prevents malformed data from affecting clients
- Transaction deduplication prevents replay attacks
