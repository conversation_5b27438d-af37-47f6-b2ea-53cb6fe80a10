# SolanaTracker Worker Guide

## Overview

The SolanaTracker worker (`src/workers/solanaTracker.js`) provides an efficient proxy for the SolanaTracker WebSocket API with advanced connection pooling capabilities. This worker enables real-time streaming of Solana blockchain data including token launches, price updates, transactions, and more.

## Key Features

### 🔄 Connection Pooling
- **Shared Connections**: Multiple users can subscribe to the same SolanaTracker endpoint without creating duplicate connections
- **Automatic Management**: Connections are created only when needed and closed when no subscribers remain
- **Resource Efficient**: Minimizes WebSocket connections to SolanaTracker API

### 🔧 Modular Architecture
- **Discrete Transformation Functions**: Each data type has its own transformation function for easy maintenance
- **Abstracted Data Sources**: Implementation details are hidden, making future refactoring simple
- **Event-Driven Integration**: Seamlessly integrates with existing StreamManager system

### 🛡️ Robust Error Handling
- **Exponential Backoff**: Intelligent reconnection strategy with jitter
- **Deduplication**: Prevents duplicate transaction processing
- **Graceful Degradation**: Handles connection failures without affecting other streams

## Architecture

### Data Flow

```
SolanaTracker WebSocket API → SolanaTracker Worker → Redis Pub/Sub → Stream Manager → WebSocket Clients
                                                  ↓
                                             Redis List Storage (last 100) → REST API History Endpoint
```

### Components

1. **SolanaTracker Worker** (`src/workers/solanaTracker.js`)
   - Manages WebSocket connections to SolanaTracker API
   - Implements connection pooling and subscriber management
   - Transforms raw data to standardized API format
   - Publishes to internal Redis channel

2. **Connection Pool Manager**
   - Tracks active connections by room/endpoint
   - Manages subscriber lists for each room
   - Handles connection lifecycle and cleanup

3. **Stream Manager Integration**
   - Subscribes to internal Redis channel
   - Broadcasts to WebSocket clients
   - Handles credit deduction per message

## Configuration

### Environment Variables

```bash
# Required for SolanaTracker
SOLANA_TRACKER_WSS_URL=wss://datastream.solanatracker.io
SOLANA_TRACKER_WSS_KEY=your-api-key-here

# Optional Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
```

### Supported Room Types

| Room Type | Description | Requires Parameters | Example |
|-----------|-------------|-------------------|---------|
| `latest` | Latest tokens/pools | No | `latest` |
| `graduating` | Graduating tokens | No | `graduating` |
| `graduated` | Graduated tokens | No | `graduated` |
| `pool` | Pool changes | Yes | `pool:poolId` |
| `transaction` | Token transactions | Yes | `transaction:tokenAddress` |
| `price` | Price updates | Yes | `price:poolId` |
| `wallet` | Wallet transactions | Yes | `wallet:walletAddress` |
| `metadata` | Token metadata | Yes | `metadata:tokenAddress` |
| `holders` | Token holders | Yes | `holders:tokenAddress` |
| `token` | Token changes | Yes | `token:tokenAddress` |

## Usage Examples

### Basic Subscription

```javascript
// Subscribe to latest tokens
const result = await streamManager.subscribeSolanaTrackerRoom('latest', 'user123');
console.log(`Subscribed to latest tokens. Total subscribers: ${result.subscriberCount}`);

// Subscribe to specific token transactions
await streamManager.subscribeSolanaTrackerRoom(
  'transaction:So11111111111111111111111111111111111111112', 
  'user123'
);
```

### Unsubscription

```javascript
// Unsubscribe from room
const result = await streamManager.unsubscribeSolanaTrackerRoom('latest', 'user123');
console.log(`Remaining subscribers: ${result.subscriberCount}`);
```

### Getting Statistics

```javascript
// Get worker statistics
const stats = streamManager.getSolanaTrackerStats();
console.log('Active connections:', stats.activeConnections);
console.log('Total subscribers:', stats.totalSubscribers);
console.log('Room details:', stats.rooms);
```

## Data Transformation

### Latest Tokens Example

**Input (SolanaTracker):**
```json
{
  "token": {
    "name": "Example Token",
    "symbol": "EXAMPLE",
    "mint": "TokenMintAddress...",
    "decimals": 6
  },
  "pools": [...],
  "events": {...},
  "risk": {...}
}
```

**Output (Standardized):**
```json
{
  "type": "latest",
  "timestamp": 1703123456789,
  "token": {
    "name": "Example Token",
    "symbol": "EXAMPLE",
    "mint": "TokenMintAddress...",
    "decimals": 6
  },
  "pools": [...],
  "events": {...},
  "risk": {...}
}
```

### Transaction Data Example

**Input (SolanaTracker):**
```json
{
  "tx": "TransactionSignature...",
  "tokenAddress": "TokenAddress...",
  "type": "buy",
  "amount": 1000000,
  "price": 0.001
}
```

**Output (Standardized):**
```json
{
  "type": "transaction",
  "timestamp": 1703123456789,
  "tx": "TransactionSignature...",
  "tokenAddress": "TokenAddress...",
  "transactionType": "buy",
  "amount": 1000000,
  "price": 0.001
}
```

## Integration with StreamManager

The SolanaTracker worker integrates seamlessly with the existing StreamManager:

1. **Initialization**: Worker is created and initialized alongside KolFeed and KafkaStreams
2. **Event-Driven Streams**: Registered as event-driven streams in StreamManager
3. **Redis Pub/Sub**: Uses internal Redis channel for communication
4. **Credit System**: Integrates with existing credit deduction system
5. **History Storage**: Stores last 100 messages per room type in Redis

## API Endpoints

### WebSocket Streams

The following streams are automatically available through the WebSocket API:

- `solana-latest` - Latest tokens and pools
- `solana-graduating` - Tokens approaching graduation
- `solana-graduated` - Recently graduated tokens

### REST API History

Historical data is available through REST endpoints:

```bash
# Get latest tokens history
GET /api/streams/solana-latest/history

# Get graduating tokens history  
GET /api/streams/solana-graduating/history

# Get graduated tokens history
GET /api/streams/solana-graduated/history
```

## Monitoring and Maintenance

### Health Checks

```javascript
// Check if worker is running
const stats = streamManager.getSolanaTrackerStats();
console.log('Worker status:', stats.isRunning);

// Check connection health
for (const [room, details] of Object.entries(stats.rooms)) {
  if (!details.isConnected) {
    console.warn(`Connection issue in room: ${room}`);
  }
}
```

### Maintenance Tasks

The worker automatically performs maintenance:

- **Transaction Deduplication**: Cleans up old transaction IDs
- **Connection Cleanup**: Removes unused connections
- **Reconnection Management**: Handles failed connections with exponential backoff

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check `SOLANA_TRACKER_WSS_URL` and `SOLANA_TRACKER_WSS_KEY`
   - Verify SolanaTracker API subscription status
   - Check network connectivity

2. **No Data Received**
   - Verify room name format (e.g., `latest`, `transaction:tokenAddress`)
   - Check SolanaTracker API rate limits
   - Review worker logs for errors

3. **High Memory Usage**
   - Monitor transaction deduplication set size
   - Check for connection leaks in pool
   - Review subscriber cleanup

### Debug Logging

Enable debug logging in development:

```bash
NODE_ENV=development
```

This will show detailed logs for:
- Connection establishment
- Message processing
- Subscriber management
- Reconnection attempts

## Performance Considerations

### Connection Pooling Benefits

- **Reduced API Calls**: Shared connections minimize SolanaTracker API usage
- **Lower Latency**: Existing connections provide faster data delivery
- **Resource Efficiency**: Fewer WebSocket connections reduce memory usage

### Scalability

- **Horizontal Scaling**: Multiple instances can run with Redis coordination
- **Load Distribution**: Connection pooling distributes load efficiently
- **Memory Management**: Automatic cleanup prevents memory leaks

## Security

### API Key Management

- Store `SOLANA_TRACKER_WSS_KEY` securely in environment variables
- Rotate API keys regularly
- Monitor API usage for anomalies

### Data Validation

- All incoming data is validated and transformed
- Error handling prevents malformed data from affecting clients
- Transaction deduplication prevents replay attacks
