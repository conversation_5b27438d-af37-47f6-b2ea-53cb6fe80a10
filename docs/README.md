# StalkAPI Documentation

Welcome to the comprehensive documentation for the StalkAPI engine - a production-ready API platform with credit-based usage tracking, multi-tier access control, and real-time WebSocket streaming.

## 📖 Documentation Index

### 🚀 Getting Started
- **[Setup Guide](SETUP_GUIDE.md)** - Complete installation and configuration instructions
- **[API Documentation](API_DOCUMENTATION.md)** - Full API reference with examples and authentication

### 🏗️ Architecture & Overview
- **[Project Summary](PROJECT_SUMMARY.md)** - Comprehensive project overview, architecture, and technical details

### 💳 Credit System
- **[Credit System Guide](CREDIT_SYSTEM_GUIDE.md)** - Complete guide to the credit-based usage tracking system
  - How credits work
  - Configuration and pricing
  - WebSocket stream credit management
  - Admin management
  - Analytics and monitoring

### 📊 Real-Time Data Streams
- **[KOL Feed Guide](KOL_FEED_GUIDE.md)** - Real-time KOL trading activity integration
  - KOL feed setup and configuration
  - Real-time WebSocket streaming
  - Historical data REST API endpoint
  - Message format and data structure
  - Credit management for streams
  - Access requirements and troubleshooting
- **[KOL Feed History API](KOL_FEED_HISTORY_API.md)** - REST API for historical KOL trading data
- **[Kafka Streams Guide](KAFKA_STREAMS_GUIDE.md)** - Real-time Kafka data streams integration
- **[SolanaTracker Guide](SOLANA_TRACKER_GUIDE.md)** - SolanaTracker WebSocket API proxy with connection pooling
  - Connection pooling for efficient resource usage
  - Modular data transformation functions
  - Support for all SolanaTracker room types
  - Real-time Solana blockchain data streaming

### 🧪 Testing & Development
- **[Postman Guide](POSTMAN_GUIDE.md)** - API testing with Postman collections and environments

### 🚀 Deployment
- **[Nginx Deployment](NGINX_DEPLOYMENT.md)** - Production deployment guide with Cloudflare integration

## 🎯 Quick Navigation

### For Developers
1. Start with [Setup Guide](SETUP_GUIDE.md) to get the API running
2. Review [API Documentation](API_DOCUMENTATION.md) for endpoint details
3. Use [Postman Guide](POSTMAN_GUIDE.md) for testing
4. Understand [Credit System Guide](CREDIT_SYSTEM_GUIDE.md) for usage tracking

### For System Administrators
1. Review [Project Summary](PROJECT_SUMMARY.md) for architecture overview
2. Follow [Nginx Deployment](NGINX_DEPLOYMENT.md) for production setup
3. Study [Credit System Guide](CREDIT_SYSTEM_GUIDE.md) for user management
4. Use admin endpoints in [API Documentation](API_DOCUMENTATION.md)

### For API Users
1. Check [API Documentation](API_DOCUMENTATION.md) for endpoint reference
2. Understand credit costs in [Credit System Guide](CREDIT_SYSTEM_GUIDE.md)
3. Use [Postman Guide](POSTMAN_GUIDE.md) for testing your integration

## 🔧 Key Features Covered

### Authentication & Security
- JWT token authentication
- API key authentication
- Admin API key system
- Permission-based access control
- Rate limiting and security headers

### Credit System
- Usage-based billing
- Tier-based credit allocation
- Real-time credit tracking
- Monthly credit resets
- Admin credit management

### Multi-Tier Access Control
- Free, Basic, Premium, Enterprise tiers
- Endpoint access restrictions
- WebSocket stream permissions
- Tier enable/disable functionality

### Real-Time Streaming
- WebSocket connections with credit-based message delivery
- Redis pub/sub architecture for scalable streaming
- Stream subscriptions with tier-based access control
- **KOL Feed**: Real-time trading activity from Key Opinion Leaders
- **Kafka Streams**: Real-time Jupiter AMM swaps, Pump.fun swaps, and DCA orders
- **SolanaTracker**: Real-time Solana blockchain data with connection pooling
- Stream credit management (credits per message received)
- Automatic credit warnings for insufficient balance

### Admin Management
- Dedicated admin authentication
- Tier management
- User credit administration
- Usage analytics
- System monitoring

## 📊 API Overview

### Core Endpoints
- **Authentication**: `/auth/*` - Login, registration, profile management
- **API v1**: `/api/v1/*` - Main API endpoints with credit consumption
- **WebSocket API**: `/ws-api/*` - WebSocket connection and stream credit management
- **Admin API**: `/admin/*` - Administrative functions (admin key required)

#### New WebSocket Credit Endpoints
- `GET /ws-api/credits` - Get stream credit information and costs
- `GET /ws-api/credits?stream=kol-feed` - Get specific stream credit details
- `GET /ws-api/credits/stats` - Get stream credit usage statistics

### Access Tiers
| Tier | Monthly Credits | Price | Features |
|------|----------------|-------|----------|
| **Free** | 1,000 | $0.00 | Basic endpoints, 1 WebSocket |
| **Basic** | 1,000,000 | $49.99 | More endpoints, 3 WebSockets |
| **Premium** | 5,000,000 | $149.99 | Advanced features, 5 WebSockets |
| **Enterprise** | Unlimited | $499.99 | All features, 10 WebSockets |

### Credit Costs

#### REST API Endpoints
| Endpoint | Credits | Description |
|----------|---------|-------------|
| `/api/v1/demo` | 1 | Basic demo functionality |
| `/api/v1/data` | 2 | Data retrieval |
| `/api/v1/analytics` | 5 | Analytics processing |
| `/api/v1/search` | 2 | Search functionality |
| `/api/v1/submit` | 3 | Data submission |
| `/api/v1/kol-feed/history` | 3 | KOL trading history |
| `/api/v1/batch` | 10 | Batch processing |

#### WebSocket Streams
| Stream | Credits per Message | Description |
|--------|-------------------|-------------|
| `demo-stream` | 1 | Demo data for testing |
| `data-stream` | 2 | Real-time data feed |
| **`kol-feed`** | **2** | **Real-time KOL trading activity** |
| **`jupiter-amm-swaps`** | **2** | **Real-time Jupiter AMM swaps** |
| **`pumpfun-amm-swaps`** | **2** | **Real-time Pump.fun AMM swaps** |
| **`jupiter-dca-orders`** | **2** | **Real-time Jupiter DCA orders** |
| **`solana-latest`** | **2** | **Latest Solana tokens/pools** |
| **`solana-graduating`** | **2** | **Graduating Solana tokens** |
| **`solana-graduated`** | **2** | **Graduated Solana tokens** |
| `analytics-stream` | 5 | Analytics metrics |
| `enterprise-stream` | 1 | High-frequency enterprise data |

**Note**: WebSocket connections are free, but stream messages cost credits per message received.

## 🛠️ Technical Stack

- **Backend**: Node.js with Express
- **Database**: PostgreSQL with atomic functions
- **Cache**: Redis for performance and pub/sub
- **WebSockets**: Native WebSocket with Redis pub/sub
- **Authentication**: JWT tokens and API keys
- **Deployment**: Nginx reverse proxy with Cloudflare

## 📈 Monitoring & Analytics

- Real-time credit usage tracking
- WebSocket connection monitoring
- Admin analytics dashboard
- Usage pattern analysis
- Performance metrics

## 🔗 External Resources

- **[Postman Collections](../postman/)** - Complete testing collections
- **[User API Collection](../postman/StalkAPI_User_Collection.json)** - End-user API testing
- **[Admin API Collection](../postman/StalkAPI_Admin_Collection.json)** - Internal management
- **[Environment Variables](../postman/StalkAPI_Postman_Environment.json)** - Configuration settings
- **[SQL Scripts](../SQL/)** - Database setup and migrations
- **[Nginx Config](../nginx.config)** - Production server configuration

## 🆘 Getting Help

1. **Check the documentation** - Most questions are answered in these guides
2. **Review error messages** - The API provides detailed error responses
3. **Test with Postman** - Use the provided collection for debugging
4. **Check logs** - Application logs provide detailed information
5. **Monitor health** - Use `/health` endpoint for system status

## 📝 Contributing

When contributing to the documentation:

1. Keep examples current and tested
2. Update both API docs and guides when adding features
3. Include error handling examples
4. Add new endpoints to Postman collection
5. Update credit costs if applicable

---

**Last Updated**: June 2025  
**Version**: 1.0.0  
**Status**: Production Ready

For the latest updates and source code, visit the project repository.
