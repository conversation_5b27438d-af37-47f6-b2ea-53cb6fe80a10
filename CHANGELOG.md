# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **SolanaTracker Worker Module** - New efficient proxy for SolanaTracker WebSocket API
  - Implemented connection pooling for WebSocket streams to minimize API usage
  - Created modular data transformation functions for easy maintenance and refactoring
  - Added support for all SolanaTracker room types (latest, graduating, graduated, pool, transaction, price, wallet, metadata, holders, token)
  - Integrated with existing StreamManager system following established patterns
  - Added proper error handling, reconnection logic with exponential backoff, and logging
  - Implemented transaction deduplication to prevent duplicate processing
  - **Premium+ tier feature with 0 credit cost** - Included as part of Premium and Enterprise subscriptions
  - **Real-time only** - No historical data storage to optimize performance and reduce complexity
  - Added comprehensive documentation in `docs/SOLANA_TRACKER_GUIDE.md`

### Technical Implementation
- **Connection Pooling System**:
  - Multiple users can subscribe to the same SolanaTracker endpoint without creating duplicate connections
  - Connections are automatically created when needed and closed when no subscribers remain
  - Efficient resource management with subscriber tracking per room
  
- **Modular Architecture**:
  - Discrete transformation functions for each data type (latest, transactions, price updates, etc.)
  - Abstracted data source details for future flexibility
  - Well-named functions that can be easily refactored
  
- **Integration Features**:
  - Follows KolFeed and KafkaStreams worker patterns
  - Uses Redis pub/sub for internal communication
  - Stores historical data in Redis lists (last 100 messages per stream)
  - Integrates with StreamManager's event-driven system
  - Supports dynamic subscription/unsubscription

### Environment Variables
- Added `SOLANA_TRACKER_WSS_URL` - WebSocket URL for SolanaTracker API (wss://datastream.solanatracker.io)
- Added `SOLANA_TRACKER_WSS_KEY` - API key for SolanaTracker authentication

### New Streams Available (Premium+ Tier, 0 Credits)
- `solana-latest` - Latest tokens and pools from Solana blockchain
- `solana-graduating` - Tokens approaching graduation on Pump.fun
- `solana-graduated` - Recently graduated tokens

### Database Updates
- Added SolanaTracker stream definitions to `stream_definitions` table
- Set tier requirement to "premium" (available for Premium and Enterprise tiers)
- Set credit cost to 0 for all SolanaTracker streams (included feature)

### Documentation Updates
- Created comprehensive `docs/SOLANA_TRACKER_GUIDE.md` with:
  - Architecture overview and data flow diagrams
  - Configuration instructions and environment variables
  - Usage examples and API integration guide
  - Data transformation examples
  - Troubleshooting and performance considerations
- Updated `docs/README.md` to include SolanaTracker worker information
- Added SolanaTracker streams to WebSocket streams documentation

### Live Testing Results
- **✅ Connection Success**: WebSocket URL format `WSS_URL/API_KEY` working perfectly
- **✅ Data Structure Validation**: 11+ real messages captured, structure confirmed compatible
- **✅ Connection Pooling Verified**: Multiple subscribers (5) sharing single connection
- **✅ Partial Unsubscription**: Connection maintained with remaining subscribers
- **✅ Complete Unsubscription**: Proper connection cleanup when all users leave
- **✅ Multiple Rooms**: Support for latest, graduating, graduated rooms confirmed
- **✅ Real-time Data**: Successfully receiving live token data from Pump.fun, Meteora, Raydium

### Files Modified
- `src/workers/solanaTracker.js` - New worker module (700+ lines)
- `src/websocket/StreamManager.js` - Integrated SolanaTracker worker
- `tests/test_solana_tracker_live.js` - Comprehensive live testing suite
- `tests/test_connection_pooling.js` - Connection pooling validation tests
- `tests/debug_solana_tracker_connection.js` - Connection debugging utility
- `database/add_solana_tracker_streams.sql` - SQL statements for database stream definitions
- `docs/SOLANA_TRACKER_GUIDE.md` - New comprehensive documentation
- `docs/README.md` - Updated with SolanaTracker information
- `CHANGELOG.md` - This changelog

### Code Quality
- Follows established patterns from existing workers
- Comprehensive error handling and logging
- Modular design for easy maintenance
- Connection pooling for efficient resource usage
- Automatic cleanup and maintenance routines

---

## Previous Versions

*Previous changelog entries would be documented here as the project evolves*
