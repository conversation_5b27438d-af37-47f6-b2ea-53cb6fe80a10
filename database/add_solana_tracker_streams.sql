-- Add SolanaTracker streams to stream_definitions table
-- These streams are Premium+ tier features with 0 credit cost

-- Insert solana-latest stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'solana-latest',
    'Latest tokens and pools from Solana blockchain via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert solana-graduating stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'solana-graduating',
    'Tokens approaching graduation on Pump.fun via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert solana-graduated stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'solana-graduated',
    'Recently graduated tokens from Pump.fun via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Verify the insertions
SELECT 
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at
FROM stream_definitions 
WHERE stream_name IN ('solana-latest', 'solana-graduating', 'solana-graduated')
ORDER BY stream_name;
