-- Add SolanaTracker streams to stream_definitions table
-- These streams are Premium+ tier features with 0 credit cost

-- Insert tokens-launched stream (latest tokens/pools)
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'tokens-launched',
    'Latest tokens and pools launched on Solana blockchain via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert tokens-graduating stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'tokens-graduating',
    'Tokens approaching graduation on Pump.fun/Moonshot via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert tokens-graduated stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'tokens-graduated',
    'Recently graduated tokens from Pump.fun/Moonshot via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert pool-changes stream (pool updates)
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'pool-changes',
    'Real-time updates about changes in specific pools via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert token-transactions stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'token-transactions',
    'Real-time transactions for specific tokens via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert price-updates stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'price-updates',
    'Real-time price updates for specific pools or tokens via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert wallet-transactions stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'wallet-transactions',
    'Real-time transaction updates for specific wallets via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert token-metadata stream (BETA)
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'token-metadata',
    'Complete token metadata updates via SolanaTracker API (BETA)',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert token-holders stream (BETA)
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'token-holders',
    'Token holder count changes via SolanaTracker API (BETA)',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Insert token-changes stream
INSERT INTO stream_definitions (
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at,
    updated_at
) VALUES (
    'token-changes',
    'All updates from any pool for specific tokens via SolanaTracker API',
    'premium',
    0,
    true,
    NOW(),
    NOW()
) ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    tier_requirement = EXCLUDED.tier_requirement,
    credit_cost = EXCLUDED.credit_cost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Verify the insertions
SELECT
    stream_name,
    description,
    tier_requirement,
    credit_cost,
    is_active,
    created_at
FROM stream_definitions
WHERE stream_name IN (
    'tokens-launched', 'tokens-graduating', 'tokens-graduated',
    'pool-changes', 'token-transactions', 'price-updates',
    'wallet-transactions', 'token-metadata', 'token-holders', 'token-changes'
)
ORDER BY stream_name;
