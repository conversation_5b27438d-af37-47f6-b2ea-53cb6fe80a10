{"timestamp": "2025-06-05T01:02:06.110Z", "testResults": {"dataStructures": {}, "connectionPooling": {}, "errors": ["Data structure test failed for latest: Failed to create connection for room: latest", "Data structure test failed for graduating: Failed to create connection for room: graduating", "Data structure test failed for graduated: Failed to create connection for room: graduated", "Connection pooling test failed: Failed to create connection for room: latest"]}, "capturedData": {"latest": 0, "graduating": 0, "graduated": 0, "transactions": 0, "prices": 0}}